<?php

namespace App\Http\Controllers;

use App\Models\Profile;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class AuthController extends Controller
{
    function set_password(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required|min:8',
            'scenario' => 'sometimes|in:login,register', // Optional parameter to indicate scenario
        ]);
        if ($validator->fails()) {
            return api_response(false, $validator->errors()->first());
        }

        $user = User::where('email', $request->email)->first();

        if ($user) {
            // User exists - this is a login scenario
            if (!Hash::check($request->password, $user->password)) {
                return api_response(false, "Invalid password");
            }

            // Login the user
            Auth::login($user);
            $roleName = $user->getRoleNames()->first();

            return response()->json([
                'status' => true,
                'message' => "Login successful",
                'role' => $roleName,
                'scenario' => 'login'
            ]);
        } else {
            // User doesn't exist - this should not happen in normal flow
            // But we'll handle it as registration scenario
            return api_response(false, "User not found. Please complete email verification first.");
        }
    }

    function registerUserType($user_type)
    {
        $role = auth()->user()->roles->first()->name ?? null;
        if ($role && $role == $user_type) {
            return view("auth.register." . $user_type);
        } else {
            abort(403, 'Unauthorized action.');
        }
    }

    function registerProfessional(Request $request)
    {
        $step_one = [
            'full_name' => 'required',
            'company_name' => 'required',
            'phone' => 'required',
            'website' => 'required|url',
            'facebook' => 'url',
            'instagram' => 'url',
            'tiktok' => 'url',
            "location" => 'required',
            "lat" => 'required',
            "lng" => 'required',
            "location_service" => 'required',
            "company_id" => 'required',
            "vat_number" => 'required',
        ];
        $step_two = [
            'bank_name' => 'required',
            'account_number' => 'required',
            'sort_code' => 'required',
            'iban' => 'required',
            'swift' => 'required',
        ];
        $validator = Validator::make($request->all(), [
            'full_name' => 'required',
            'company_name' => 'required',
            'phone' => 'required',
            'website' => 'required|url',
            'facebook' => 'url',
            'instagram' => 'url',
            'tiktok' => 'url',
            "location" => 'required',
            "lat" => 'required',
            "lng" => 'required',
            "location_service" => 'required',
            "company_id" => 'required',
            "vat_number" => 'required',
        ]);
        if ($validator->fails()) {
            return api_response(false, $validator->errors()->first());
        }
    }

    function registerCustomer(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'avatar' => 'required|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'fullname' => 'required',
            'email' => 'email|required',
            'password' => 'required|min:8',
            'phone' => 'required',
            'services' => 'required',
        ]);
        if ($validator->fails()) {
            return redirect()->back()
                ->with([
                    'error' => 'Validation Error',
                    'message' => $validator->errors()->first(),
                     'type' => 'error',
                ])
                ->withInput();
        }
        $user = User::find(auth()->id());
        $user->name = $request->fullname;
        $user->email = $request->email;
        $user->password = Hash::make($request->password);
        $user->save();
        $profile = $user->profile;
        if ($profile == null) {
            $profile = new Profile();
            $profile->phone = $request->phone;
            $profile->service_preferences = $request->services;
            if (isset($request->avatar)) {
                $file = $request->avatar;
                $extension = $file->extension() ?: 'png';
                $destinationPath = public_path() . '/storage/uploads/users/';
                $safeName = Str::random(10) . '.' . $extension;
                $file->move($destinationPath, $safeName);
                $profile->pic = $safeName;
            } else {
                $profile->pic = 'no_avatar.jpg';
            }
            $profile->save();
        }
        $profile->user_id = $user->id;
        $profile->save();
        return redirect()->route('dashboard')->with([
            'title' => 'Done',
            'message' => 'You have been registered successfully',
            'type' => 'success',
        ]);
    }
}

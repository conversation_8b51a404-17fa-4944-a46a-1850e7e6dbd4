<?php

namespace App\Http\Controllers;

use App\Mail\EmailVerificationMail;
use App\Models\EmailVerification;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Spatie\Permission\Models\Role;

class EmailVerificationController extends Controller
{
    public function sendOtp(Request $request)
    {
        $validation = Validator::make($request->all(), [
            'email' => 'required|email',
        ]);
        if ($validation->fails()) {
            return api_response(false, $validation->errors()->first());
        }

        $email = $request->email;

        // Check if user already exists
        $existingUser = \App\Models\User::where('email', $email)->first();

        if ($existingUser) {
            // Email exists - this is a login scenario
            return api_response(true, "Email found. Please enter your password.", [
                'scenario' => 'login',
                'user_exists' => true
            ]);
        }

        // Email doesn't exist - this is a registration scenario, send OTP
        $plainToken = rand(100000, 999999);
        $encryptedToken = encrypt($plainToken);
        EmailVerification::updateOrCreate(
            ['email' => $email],
            [
                'token' => $encryptedToken,
                'expires_at' => now()->addMinutes(5),
            ]
        );
        Mail::to($email)->send(new EmailVerificationMail($plainToken));
        return api_response(true, "OTP Sent", [
            'scenario' => 'register',
            'user_exists' => false
        ]);
    }

    public function verifyOtp(Request $request)
    {
        $validation = Validator::make($request->all(), [
            'email' => 'required|email|exists:email_verifications,email',
            'otp' => 'required|digits:6',
            "user_type" => 'required|in:customer,professional',
        ]);
        if ($validation->fails()) {
            return api_response(false, $validation->errors()->first());
        }
        $record = EmailVerification::where('email', $request->email)->first();

        if (!$record) {
            return api_response(false, "OTP not found");
        }

        if (Carbon::parse($record->expires_at)->isPast()) {
            return api_response(false, "OTP expired");
        }

        try {
            $decryptedToken = decrypt($record->token);
        } catch (\Exception $e) {
            return api_response(false, "Invalid OTP token");
        }
        if ($decryptedToken != $request->otp) {
            return api_response(false, "Invalid OTP");
        }
        $record->delete();
        $user = new User();
        $user->name = "-";
        $user->email = $request->email;
        $user->email_verified_at = now();
        $user->save();

        $role = Role::where("name",$request->user_type)->first();
        $user->assignRole($role);
        Auth::login($user);
        return api_response(true, "OTP verified successfully", ["url" => route('register.user_type', ["user_type" => $request->user_type])]);
    }
}

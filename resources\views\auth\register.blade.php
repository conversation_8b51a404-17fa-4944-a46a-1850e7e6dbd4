@extends('layouts.app')

@section('content')
    <div class="container-fluid first-stepper-form ">
        <div class="row">
            <div class="col-md-6 p-20">
                <form class="register-form form w-100 mb-10" novalidate="novalidate" method="POST" method="POST"
                    action="{{ route('register') }}">
                    <div>
                        <i name="previous" value="ll" class="fas fa-chevron-left previous action-button-previous"></i>
                    </div>

                    @csrf
                    <fieldset class="step-1">
                        <h3 class="text-center mb-10">Sign up/Login</h3>

                        <label class="w-100">
                            <input type="radio" name="user_type" value="customer" class="card-radio">
                            <div
                                class="gray-card d-flex justify-content-between align-items-center mb-5 next action-button">
                                <div>
                                    <h5 class="fs-16 fw-600">For customers</h5>
                                    <p class="fs-14 gray-text">Book salons and spas near you</p>
                                </div>
                                <img src="{{ asset('website') }}/assets/images/right-arrow.svg" class="next action-button"
                                    alt="icon">
                            </div>
                        </label>

                        <label class="w-100">
                            <input type="radio" name="user_type" value="professional" class="card-radio">
                            <div class="gray-card d-flex justify-content-between align-items-center next action-button">
                                <div>
                                    <h5 class="fs-16 fw-600">For professionals</h5>
                                    <p class="fs-14 gray-text">Book salons and spas near you</p>
                                </div>
                                <img src="{{ asset('website') }}/assets/images/right-arrow.svg" class="next action-button"
                                    alt="icon">
                            </div>
                        </label>


                        <!-- <div class="gray-card d-flex justify-content-between align-items-center mb-5 next action-button"
                                                                                value="Next">
                                                                                <div>
                                                                                    <h5 class="fs-16 fw-600">For customers</h5>
                                                                                    <p class="fs-14 gray-text">Book salons and spas near you</p>
                                                                                </div>
                                                                                <img src="{{ asset('website') }}/assets/images/right-arrow.svg" class="next action-button"
                                                                                    value="Next" alt="icon">
                                                                            </div> -->

                        <!-- <div class="gray-card d-flex justify-content-between align-items-center next action-button"
                                                                                value="Next">
                                                                                <div>
                                                                                    <h5 class="fs-16 fw-600">For professionals</h5>
                                                                                    <p class="fs-14 gray-text">Book salons and spas near you</p>
                                                                                </div>
                                                                                <img src="{{ asset('website') }}/assets/images/right-arrow.svg" class="next action-button"
                                                                                    alt="icon">
                                                                            </div> -->
                    </fieldset>

                    <fieldset class="step-2">
                        <div class="mb-10">
                            <h3 class="text-center">Stylenest for professionals</h3>
                            <p class="fs-14 text-center">Create an account or log in to manage your business.</p>
                        </div>

                        <div class="mb-8">
                            <input id="email" type="email" placeholder="Enter your email address"
                                class="form-control mb-6 bg-transparent @error('email') is-invalid @enderror" name="email"
                                value="{{ old('email') }}" required autocomplete="email">

                            @error('name')
                                <span class="invalid-feedback" role="alert">
                                    <strong>{{ $message }}</strong>
                                </span>
                            @enderror

                            <button type="button" class="action-button blue-btn fs-6 fw-600 send-otp-btn"> Continue
                            </button>
                        </div>

                        <div class="separator separator-content my-14">
                            <span class="w-125px text-gray-500 fw-semibold fs-7">OR</span>
                        </div>

                        <div class="mb-5">
                            <a href="#"
                                class="btn btn-flex mb-5 btn-outline btn-text-gray-700 bg-state-light flex-center text-nowrap w-100">
                                <img class="pe-4" src="{{ asset('website') }}/assets/images/Google_Logo.svg"
                                    alt="icon"> Sign
                                in with Google</a>

                            <a href="#"
                                class="btn btn-flex btn-outline btn-text-gray-700 bg-state-light flex-center text-nowrap w-100">
                                <img class="pe-4" src="{{ asset('website') }}/assets/images/Apple_Logo.svg"
                                    alt="icon"> Sign
                                in with Apple</a>
                        </div>

                    </fieldset>

                    <fieldset class="step-3">
                        <div class="mb-10">
                            <h3 class="text-center">Verify your identity</h3>
                            <p class="fs-14 text-center">To protect your account, we'll send a text message with a 4-
                                digit code <NAME_EMAIL>.</p>
                        </div>

                        <div class="mb-8 position-relative">
                            <label for="otp" class="fs-14 fw-500 mb-3">Enter OTP</label>

                            <input id="otp" type="number" placeholder="Enter OTP"
                                class="form-control mb-6 bg-transparent pe-5" name="otp">

                            <span id="toggle-password-otp"
                                class="btn-sm btn-icon position-absolute top-50 translate-middle-y end-0 pt-6 me-3 cursor-pointer">
                                <i class="fa-solid fa-eye"></i>
                                <i class="fa-solid fa-eye-slash d-none"></i>
                            </span>

                            <button type="button" class="verify-otp-btn action-button blue-btn fs-6 fw-600 mt-3">Verify
                                OTP</button>
                        </div>
                    </fieldset>

                    <fieldset class="step-4">
                        <div class="mb-10">
                            <h3 class="text-center">Password</h3>
                            <p class="fs-14 text-center">Just type in your password to log in!</p>
                        </div>

                        <div class="mb-10">
                            <label class="fs-14 fw-500 text-start mb-3">Enter Password</label>
                            <input id="password" type="password" placeholder="Password"
                                class="form-control mb-5 bg-transparent @error('password') is-invalid @enderror"
                                name="password" required autocomplete="new-password">
                            @error('password')
                                <span class="invalid-feedback" role="alert">
                                    <strong>{{ $message }}</strong>
                                </span>
                            @enderror
                            <span id="toggle-password"
                                class=" btn-sm btn-icon position-absolute translate-middle mb-8 end-0 pb-20 pe-2">
                                <i class="fa-solid fa-eye"></i>
                                <i class="fa-solid fa-eye-slash d-none"></i>
                            </span>

                            <!-- <button type="button" class="blue-btn fs-6 fw-600" value="Submit"> Login</button> -->

                        </div>

                        <button class="set-password-btn blue-btn fs-6 fw-600 w-100 d-block"> Register</button>

                    </fieldset>

                </form>

                <div class="site_logo">
                    <a href="{{ url('/') }}" class="text-center">
                        <img src="{{ asset('website') }}/assets/images/logo.png" alt="icon">
                        <h4 class="blue-text pt-2"> Stylenest </h4>
                    </a>
                    <ul>
                        <li> <a href="{{ 'terms' }}" class="blue-text">Terms of Use</a></li>
                        <li><a href="{{ '#!' }}" class="blue-text">Support</a></li>
                        <li><a href="{{ 'privacy_policy' }}" class="blue-text">Privacy policy</a></li>
                    </ul>
                </div>
            </div>

            <div class="col-md-6 login-side-image">
                <img src="{{ asset('website') }}/assets/images/login-banner.png" alt="icon">
            </div>
        </div>
    </div>
@endsection

@push('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>

    <script>
        $(document).ready(function() {
            $('#toggle-password').on('click', function() {
                var passwordField = $('#password');
                var passwordFieldType = passwordField.attr('type');

                if (passwordFieldType === 'password') {
                    passwordField.attr('type', 'text');
                    $(this).find('.fa-eye-slash').removeClass('d-none');
                    $(this).find('.fa-eye').addClass('d-none');
                } else {
                    passwordField.attr('type', 'password');
                    $(this).find('.fa-eye').removeClass('d-none');
                    $(this).find('.fa-eye-slash').addClass('d-none');
                }
            });

            $('#toggle-password-otp').on('click', function() {
                var passwordField = $('#otp');
                var passwordFieldType = passwordField.attr('type');

                if (passwordFieldType === 'password') {
                    passwordField.attr('type', 'text');
                    $(this).find('.fa-eye-slash').removeClass('d-none');
                    $(this).find('.fa-eye').addClass('d-none');
                } else {
                    passwordField.attr('type', 'password');
                    $(this).find('.fa-eye').removeClass('d-none');
                    $(this).find('.fa-eye-slash').addClass('d-none');
                }
            });

            var current_fs, next_fs, previous_fs; // Fieldsets
            var opacity;
            var current = 1;
            var steps = $("fieldset").length;

            setProgressBar(current);
            togglePreviousButton(current);

            function next_step(element) {
                current_fs = element.closest('fieldset');
                next_fs = current_fs.next('fieldset');

                next_fs.show();
                current_fs.animate({
                    opacity: 0
                }, {
                    step: function(now) {
                        opacity = 1 - now;
                        current_fs.css({
                            'display': 'none',
                            'position': 'relative'
                        });
                        next_fs.css({
                            'opacity': opacity
                        });
                    },
                    duration: 500
                });
                setProgressBar(++current);
                togglePreviousButton(current);
            }
            $(".next").click(function() {
                next_step($(this))
            });

            $(".previous").click(function() {
                current_fs = $("fieldset:visible");
                previous_fs = current_fs.prev('fieldset');

                previous_fs.show();
                current_fs.animate({
                    opacity: 0
                }, {
                    step: function(now) {
                        opacity = 1 - now;
                        current_fs.css({
                            'display': 'none',
                            'position': 'relative'
                        });
                        previous_fs.css({
                            'opacity': opacity
                        });
                    },
                    duration: 500
                });
                setProgressBar(--current);
                togglePreviousButton(current);
            });

            function setProgressBar(curStep) {
                var percent = parseFloat(100 / steps) * curStep;
                percent = percent.toFixed();
                $(".progress-bar").css("width", percent + "%");
            }

            function togglePreviousButton(curStep) {
                if (curStep === 1) {
                    $(".previous").hide();
                } else {
                    $(".previous").show();
                }
            }

            $(".submit").click(function() {
                return false;
            });

            // Send OTP Code
            let debounceTimeout;
            $(document).on("click", ".send-otp-btn", function() {
                clearTimeout(debounceTimeout);
                const element = $(this);
                debounceTimeout = setTimeout(() => {
                    const email = $("#email").val();

                    // Disable button & add spinner
                    element.prop("disabled", true);
                    const originalHtml = element.html();
                    element.html(
                        `<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Sending...`
                    );

                    $.ajax({
                        url: "{{ route('send_otp') }}",
                        type: "GET",
                        data: {
                            email: email
                        },
                        success: function(response) {
                            if (response.status == true) {
                                // Swal.fire({
                                //     title: "Success",
                                //     text: response.message,
                                //     icon: "success"
                                // });
                                next_step(element);
                            } else {
                                Swal.fire({
                                    title: "Error",
                                    text: response.message,
                                    icon: "error"
                                });
                            }
                        },
                        error: function(xhr, status, error) {
                            console.log(error);
                        },
                        complete: function() {
                            // Re-enable button & restore text
                            element.prop("disabled", false);
                            element.html(originalHtml);
                        }
                    });
                }, 300); // debounce delay 300ms
            });
            // Send OTP Code End

            // Verify OTP
            let verifyDebounceTimeout;
            $(document).on("click", ".verify-otp-btn", function() {
                clearTimeout(verifyDebounceTimeout);
                const element = $(this);

                verifyDebounceTimeout = setTimeout(() => {
                    const otp = $("#otp").val();
                    if (!otp) {
                        Swal.fire({
                            title: "Error",
                            text: "Please enter the OTP.",
                            icon: "error",
                        });
                        return;
                    }

                    element.prop("disabled", true);
                    let selectedUserType = $("input[name='user_type']:checked").val() || "";
                    const originalHtml = element.html();
                    element.html(
                        `<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Verifying...`
                    );

                    $.ajax({
                        url: "{{ route('verify_otp') }}",
                        type: "POST",
                        data: {
                            _token: "{{ csrf_token() }}",
                            otp: otp,
                            email: $("#email").val(),
                            user_type: selectedUserType
                        },
                        success: function(response) {
                            if (response.status === true) {
                                Swal.fire({
                                    title: "Success",
                                    text: "OTP verified successfully!",
                                    icon: "success",
                                    willClose: () => {
                                        // Redirect after alert is closed
                                        window.location.href = response.data
                                            .url;
                                    }
                                });
                            } else {
                                Swal.fire({
                                    title: "Error",
                                    text: response.message,
                                    icon: "error"
                                });
                            }
                        },
                        error: function(xhr, status, error) {
                            console.log(error);
                        },
                        complete: function() {
                            element.prop("disabled", false);
                            element.html(originalHtml);
                        }
                    });

                }, 300);
            });
            // Verify OTP End

            // Set Password
            $(document).on("click", ".set-password-btn", function() {
                const element = $(this);
                const password = $("#password").val();
                if (!password) {
                    Swal.fire({
                        title: "Error",
                        text: "Please enter the password.",
                        icon: "error",
                    });
                    return;
                }

                element.prop("disabled", true);
                const originalHtml = element.html();
                element.html(
                    `<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Setting...`
                );

                var customerRegisterUrl = "{{ route('register.user_type', ['user_type' => 'customer']) }}";
                var professionalRegisterUrl =
                    "{{ route('register.user_type', ['user_type' => 'professional']) }}";

                $.ajax({
                    url: "{{ route('set_password') }}",
                    type: "POST",
                    data: {
                        _token: "{{ csrf_token() }}",
                        password: password,
                        email: $("#email").val()
                    },
                    success: function(response) {
                        if (response.status === true) {
                            var userType = response.role;
                            if (userType == "customer") {
                                window.location.href = customerRegisterUrl;
                            } else if (userType == "professional") {
                                window.location.href = professionalRegisterUrl;
                            }
                            // next_step(element);
                        } else {
                            Swal.fire({
                                title: "Error",
                                text: response.message,
                                icon: "error"
                            });
                        }
                    },
                    error: function(xhr, status, error) {
                        console.log(error);
                    },
                    complete: function() {
                        element.prop("disabled", false);
                        element.html(originalHtml);
                    }
                });
            });
            // Set Password End
        });
    </script>
@endpush
